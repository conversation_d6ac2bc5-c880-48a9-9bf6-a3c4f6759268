# Firefox Packaging Guide

This guide explains how to package the Ticket Snippet Manager extension for Firefox distribution.

## Development vs Production

### Development (Temporary Add-on)
- Load via `about:debugging` → "Load Temporary Add-on"
- Extension is removed when Firefox restarts
- Perfect for testing and development
- No signing required

### Production (Permanent Installation)
- Requires packaging as `.xpi` file
- Must be signed by Mozilla (for distribution outside of Firefox Add-ons store)
- Or distributed through Mozilla Add-ons (AMO) store

## Creating a Firefox Package (.xpi)

### Method 1: Using web-ext (Recommended)

1. **Install web-ext**
   ```bash
   npm install -g web-ext
   ```

2. **Build the extension**
   ```bash
   cd ticket-templates
   web-ext build
   ```

3. **Test in Firefox**
   ```bash
   web-ext run
   ```

### Method 2: Manual ZIP Creation

1. **Create ZIP file**
   - Select all extension files (manifest.json, *.js, *.html, *.css, icons/)
   - Create a ZIP archive
   - Rename from `.zip` to `.xpi`

2. **Verify contents**
   The .xpi file should contain:
   ```
   manifest.json
   background.js
   content.js
   popup.html
   popup.js
   popup.css
   browser-polyfill.js
   firefox-validation.js
   icons/
   ```

## Firefox Add-ons Store Submission

### Prerequisites
1. Mozilla Developer Account
2. Extension review and approval process
3. Compliance with Firefox Add-on policies

### Steps
1. **Prepare for Review**
   - Ensure all code is clean and well-documented
   - Remove any debugging code
   - Test thoroughly in Firefox

2. **Submit to AMO**
   - Visit [addons.mozilla.org/developers](https://addons.mozilla.org/developers)
   - Create new add-on submission
   - Upload .xpi file
   - Fill out metadata (description, screenshots, etc.)

3. **Review Process**
   - Automated review for basic issues
   - Manual review for complex extensions
   - May require code changes based on feedback

## Self-Distribution (Advanced)

### Signing for Self-Distribution
1. **Get API Keys**
   - Visit [addons.mozilla.org/developers/addon/api/key](https://addons.mozilla.org/developers/addon/api/key)
   - Generate API key and secret

2. **Sign with web-ext**
   ```bash
   web-ext sign --api-key=YOUR_API_KEY --api-secret=YOUR_API_SECRET
   ```

3. **Distribute Signed .xpi**
   - Users can install directly
   - No store approval needed
   - Must be signed for release Firefox

## Browser Compatibility Notes

### Manifest Differences
- Uses Manifest V2 (Firefox standard)
- `browser_action` instead of `action`
- `applications.gecko` section for Firefox metadata

### API Differences
- Uses `browser.*` APIs (standard in Firefox)
- Includes polyfill for Chrome compatibility
- Some APIs may have different behavior

### Testing Checklist
- [ ] Extension loads without errors
- [ ] Popup opens and displays correctly
- [ ] Snippets can be created, edited, and deleted
- [ ] Snippet insertion works in various input types
- [ ] Storage persists between browser sessions
- [ ] No console errors in background or content scripts

## Troubleshooting

### Common Issues
1. **Manifest validation errors**
   - Check JSON syntax
   - Verify all required fields
   - Ensure proper permissions

2. **API compatibility issues**
   - Verify browser-polyfill.js is loaded
   - Check for Firefox-specific API differences
   - Test with firefox-validation.js

3. **Content script injection failures**
   - Verify content script permissions
   - Check for CSP (Content Security Policy) conflicts
   - Test on various websites

### Debug Tools
- Firefox Developer Tools
- `about:debugging` for extension inspection
- Browser console for error messages
- `firefox-validation.js` for compatibility checks

## Resources

- [Firefox Extension Workshop](https://extensionworkshop.com/)
- [web-ext documentation](https://extensionworkshop.com/documentation/develop/web-ext-command-reference/)
- [Firefox Add-on Policies](https://extensionworkshop.com/documentation/publish/add-on-policies/)
- [Manifest V2 Documentation](https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/manifest.json)
