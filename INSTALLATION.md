# Installation Guide - Ticket Snippet Manager

## Quick Start

### Chrome/Chromium-based Browsers (Chrome, Brave, Edge)

1. **Load the Extension**
   - Open your browser and navigate to `chrome://extensions/` (or `brave://extensions/`, `edge://extensions/`)
   - Enable "Developer mode" (toggle in top right corner)
   - Click "Load unpacked"
   - Select the `ticket-templates` folder containing the extension files
   - The extension icon should appear in your browser toolbar

### Firefox

1. **Load the Extension**
   - Open Firefox and navigate to `about:debugging`
   - Click "This Firefox" in the left sidebar
   - Click "Load Temporary Add-on"
   - Navigate to the extension directory and select `manifest.json`
   - The extension icon should appear in your browser toolbar
   - **Note**: The extension will be removed when Firefox restarts (this is normal for temporary add-ons)

2. **Test the Extension**
   - Open the included `test-page.html` file in your browser
   - Click the extension icon to open the popup
   - Add a test snippet (name: "Test", text: "This is a test snippet")
   - Click in one of the editable areas on the test page
   - Click the extension icon again and click on your test snippet
   - Verify the text appears in the focused element

## Detailed Installation Steps

### Step 1: Prepare the Extension Files

Ensure you have all required files in your extension directory:

```
ticket-templates/
├── manifest.json          ✅ Extension configuration (Manifest V2 for cross-browser compatibility)
├── popup.html             ✅ User interface
├── popup.css              ✅ Interface styling
├── popup.js               ✅ Interface logic
├── content.js             ✅ Page interaction
├── background.js          ✅ Background processes
├── browser-polyfill.js    ✅ Cross-browser compatibility layer
├── firefox-validation.js  ✅ Firefox compatibility validation
├── icons/                 ✅ Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── test-page.html         ✅ Testing page
```

### Step 2: Load in Browser

#### For Brave Browser:
1. Open Brave
2. Type `brave://extensions/` in the address bar
3. Enable "Developer mode" (toggle switch in top right)
4. Click "Load unpacked"
5. Navigate to and select the `ticket-templates` folder
6. Click "Select Folder"

#### For Chrome Browser:
1. Open Chrome
2. Type `chrome://extensions/` in the address bar
3. Enable "Developer mode" (toggle switch in top right)
4. Click "Load unpacked"
5. Navigate to and select the `ticket-templates` folder
6. Click "Select Folder"

#### For Firefox Browser:
1. Open Firefox
2. Type `about:debugging` in the address bar
3. Click "This Firefox" in the left sidebar
4. Click "Load Temporary Add-on"
5. Navigate to the `ticket-templates` folder
6. Select the `manifest.json` file
7. Click "Open"
8. **Important**: The extension will be removed when Firefox restarts

### Step 3: Verify Installation

After loading the extension, you should see:
- ✅ Extension card in the extensions page
- ✅ Extension icon in the browser toolbar
- ✅ No error messages in the extension card

If you see errors:
- Check that all files are present
- Verify the manifest.json syntax
- Look at the error details in the extension card

### Step 4: Test Functionality

1. **Open Test Page**
   - Navigate to the `test-page.html` file in your browser
   - Or open any website with editable text fields

2. **Create a Snippet**
   - Click the extension icon in the toolbar
   - Click "+ Add Snippet"
   - Enter a name (e.g., "Test Snippet")
   - Enter some text (e.g., "Hello, this is a test!")
   - Click "Save"

3. **Insert a Snippet**
   - Click in any editable area (especially those with `fr-element fr-view` classes)
   - Click the extension icon again
   - Click on your saved snippet
   - Verify the text appears in the focused element

## Troubleshooting

### Extension Not Loading
- **Check file permissions**: Ensure all files are readable
- **Verify manifest.json**: Use a JSON validator to check syntax
- **Check browser console**: Look for error messages in the extensions page

### Extension Icon Not Appearing
- **Refresh extensions page**: Sometimes a refresh is needed
- **Check toolbar**: The icon might be hidden in the extensions menu
- **Pin the extension**: Right-click the extension icon and select "Pin"

### Snippets Not Inserting
- **Check target elements**: Use the test page to verify functionality
- **Enable debug mode**: Set `localStorage.setItem('snippetManagerDebug', 'true')` in browser console
- **Check browser console**: Look for error messages when attempting insertion

### Storage Issues
- **Check permissions**: Ensure the extension has storage permissions
- **Clear extension data**: Remove and reload the extension to reset storage
- **Check storage quota**: Ensure browser has available storage space

## Advanced Configuration

### Custom Icons
Replace the placeholder icons in the `icons/` directory:
1. Use the provided `icons/icon.svg` as a template
2. Create PNG files at 16x16, 32x32, 48x48, and 128x128 pixels
3. Or use the `icons/generate-icons.html` helper page

### Debug Mode
Enable debug mode to see target element highlights:
```javascript
localStorage.setItem('snippetManagerDebug', 'true');
```

### Permissions
The extension requires these permissions:
- `storage`: Save snippets persistently
- `activeTab`: Access current tab content
- `tabs`: Query and send messages to tabs
- `<all_urls>`: Work on any website
- `notifications`: Show installation and success notifications (optional)

### Firefox-Specific Notes
- Uses Manifest V2 for maximum Firefox compatibility
- Temporary add-ons are removed on browser restart (normal behavior)
- For permanent installation, the extension would need to be signed by Mozilla
- All core functionality works identically to Chrome version

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify all files are present and properly formatted
3. Test with the included `test-page.html`
4. Try reloading the extension
5. Check that you're using a supported browser (Firefox, Chrome, Brave, Edge)

## Next Steps

Once installed and working:
1. Create your commonly used snippets
2. Test on your actual ticket system
3. Customize snippets for your workflow
4. Share with your team if helpful

The extension is designed to work with Froala editor-based ticket systems but should work with most editable web elements.
