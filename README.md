# Ticket Snippet Manager

A cross-browser extension for Chrome, Firefox, and other browsers that allows users to save and manage text snippets for quick insertion into ticket systems, especially those using the Froala editor.

## Features

- **Snippet Management**: Add, edit, and delete text snippets with custom names
- **Quick Insertion**: Click to insert snippets into any element with `fr-element fr-view` classes
- **Persistent Storage**: Snippets are saved across browser sessions using the browser's storage API
- **Smart Targeting**: Automatically detects and inserts into focused editable elements
- **Visual Feedback**: Clear notifications when snippets are saved or inserted
- **Froala Editor Support**: Specifically designed to work with Froala editor used in many ticket systems

## Installation

### Chrome/Chromium-based Browsers (Chrome, Brave, Edge)

1. Clone or download this repository
2. Open your browser and navigate to `chrome://extensions/` (or `brave://extensions/`, `edge://extensions/`)
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the extension directory
5. The extension icon should appear in your browser toolbar

### Firefox

1. Clone or download this repository
2. Open Firefox and navigate to `about:debugging`
3. Click "This Firefox" in the left sidebar
4. Click "Load Temporary Add-on"
5. Navigate to the extension directory and select `manifest.json`
6. The extension icon should appear in your browser toolbar

**Note**: In Firefox, the extension will be temporary and will be removed when you restart the browser. For permanent installation, you would need to package and sign the extension through Mozilla's process.

### Creating Proper Icons (Optional)

The extension includes placeholder icons. To create proper icons:

1. Open `icons/generate-icons.html` in your browser
2. Take screenshots of each icon at the specified dimensions
3. Save them as `icon16.png`, `icon32.png`, `icon48.png`, and `icon128.png` in the `icons/` directory
4. Or use the provided `icons/icon.svg` with an image converter

## Usage

### Adding Snippets

1. Click the extension icon in your browser toolbar
2. Click the "+ Add Snippet" button
3. Enter a name and text for your snippet
4. Click "Save"

### Using Snippets

1. Navigate to a page with a ticket system (or any page with editable elements)
2. Click in an editable field (especially those with `fr-element fr-view` classes)
3. Click the extension icon
4. Click on any saved snippet to insert it into the active field

### Managing Snippets

- **Edit**: Click the "Edit" button next to any snippet
- **Delete**: Click the "Delete" button next to any snippet
- **View**: All snippets are displayed in the popup with their names and preview text

## Technical Details

### File Structure

```
ticket-templates/
├── manifest.json          # Extension manifest (Manifest V2 for cross-browser compatibility)
├── popup.html             # Extension popup interface
├── popup.css              # Popup styling
├── popup.js               # Popup functionality
├── content.js             # Content script for DOM manipulation
├── background.js          # Background script
├── browser-polyfill.js    # Cross-browser compatibility layer
├── firefox-validation.js  # Firefox compatibility validation script
├── icons/                 # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   ├── icon128.png
│   ├── icon.svg           # Master icon design
│   └── generate-icons.html # Icon generation helper
└── README.md              # This file
```

### Permissions

The extension requires the following permissions:
- `storage`: To save snippets persistently
- `activeTab`: To interact with the current tab
- `tabs`: To query and send messages to tabs
- `<all_urls>`: To work on any website
- `notifications`: To show installation and success notifications (optional)

### Target Elements

The extension primarily targets elements with the CSS classes:
- `.fr-element.fr-view` (Froala editor)
- `[contenteditable="true"]` (Generic contenteditable elements)
- `textarea` and `input[type="text"]` (Fallback for standard inputs)

### Browser Compatibility

**Fully Supported:**
- Mozilla Firefox (57+)
- Google Chrome
- Brave Browser
- Microsoft Edge (Chromium-based)
- Other Chromium-based browsers

**Cross-Browser Features:**
- Uses Manifest V2 for maximum compatibility
- Includes browser polyfill for API differences
- Unified `browser.*` API calls work across all supported browsers
- Automatic fallback between `chrome.*` and `browser.*` APIs

## Development

### Testing

1. Load the extension in developer mode
2. Navigate to a page with editable elements
3. Test snippet creation, editing, and insertion
4. Check browser console for any errors

### Debugging

- Enable debug mode by setting `localStorage.setItem('snippetManagerDebug', 'true')` in the browser console
- This will highlight target elements with blue dashed borders
- Check the browser's extension console for detailed logs
- Run `firefox-validation.js` in the console to check Firefox compatibility
- Use the browser's developer tools to inspect extension behavior

### Common Issues

1. **Snippets not inserting**: Ensure the target page has loaded completely and contains editable elements
2. **Extension not appearing**: Check that developer mode is enabled and the extension is loaded
3. **Storage issues**: Check browser storage permissions and available storage space
4. **Firefox temporary installation**: In Firefox, temporary extensions are removed on restart - this is normal behavior
5. **API compatibility**: If you see console errors about missing APIs, ensure the browser-polyfill.js is loading correctly

## Default Snippets

The extension comes with three default snippets:
1. "Thank you for reporting" - Standard acknowledgment message
2. "Issue resolved" - Resolution notification
3. "Need more information" - Information request template

## Contributing

1. Fork the repository
2. Make your changes
3. Test thoroughly with different ticket systems
4. Submit a pull request

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Support

For issues or feature requests, please check the browser console for error messages and provide detailed reproduction steps.
