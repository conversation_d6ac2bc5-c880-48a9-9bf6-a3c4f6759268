# Troubleshooting Guide - Ticket Snippet Manager

## Issue: Snippets Not Inserting

If snippets are not being inserted when clicked, follow these debugging steps:

### Step 1: Check Extension Loading

1. Open `brave://extensions/` or `chrome://extensions/`
2. Verify the extension is enabled and shows no errors
3. If there are errors, check the console logs in the extension details

### Step 2: Check Content Script Loading

1. Open the test page (`test-page.html`) or any webpage
2. Open browser Developer Tools (F12)
3. Go to the Console tab
4. Look for the message: "Snippet Manager content script loaded"
5. If not present, the content script isn't loading

### Step 3: Test Content Script Communication

1. On the test page, click "Run Debug Tests"
2. Check if "Content script: LOADED" appears
3. Click "Test Content Script" button
4. This will test direct communication with the content script

### Step 4: Check Browser Console Logs

When clicking a snippet, check the console for these messages:

**Expected popup logs:**
```
Popup: Attempting to insert snippet: {id: "...", name: "...", text: "..."}
Popup: Active tab: 123 file:///.../test-page.html
Popup: Sending message to content script
Popup: Received response from content script: {success: true}
```

**Expected content script logs:**
```
Content script: Attempting to insert snippet: [snippet text]
Content script: Found target element: DIV fr-element fr-view
Content script: Inserting into contenteditable element
Content script: Snippet insertion successful
```

### Step 5: Common Issues and Solutions

#### Issue: "Could not establish connection"
**Cause:** Content script not loaded or page not ready
**Solution:** 
- Refresh the page and try again
- Check if the page URL is supported (file:// URLs might have restrictions)
- Try on a regular website (http:// or https://)

#### Issue: "No suitable target element found"
**Cause:** No editable elements on the page
**Solution:**
- Make sure you're on a page with text inputs, textareas, or contenteditable elements
- Click inside an editable field before using the extension
- Use the test page to verify functionality

#### Issue: Content script not loading
**Cause:** Extension permissions or manifest issues
**Solution:**
- Check manifest.json for syntax errors
- Verify content_scripts section is correct
- Try reloading the extension
- Check browser console for permission errors

### Step 6: Manual Testing

You can manually test snippet insertion in the browser console:

```javascript
// Check if content script is loaded
console.log('Content script loaded:', !!window.SnippetInserter);

// Test target element detection
if (window.SnippetInserter) {
    const targets = window.SnippetInserter.findTargetElements();
    console.log('Found target elements:', targets.length);
}

// Test manual insertion
if (window.SnippetInserter) {
    window.SnippetInserter.insertSnippet('Test manual insertion');
}
```

### Step 7: Enable Debug Mode

1. Open browser console
2. Run: `localStorage.setItem('snippetManagerDebug', 'true')`
3. Refresh the page
4. Target elements should now have blue dashed borders

### Step 8: Check Permissions

Ensure the extension has these permissions:
- `storage` - for saving snippets
- `activeTab` - for accessing current tab
- `scripting` - for injecting scripts
- `<all_urls>` - for working on all websites

### Step 9: Test on Different Pages

Try the extension on:
1. The included test page (`test-page.html`)
2. A simple HTML page with a textarea
3. A real website with forms
4. A ticket system with Froala editor

### Step 10: Reset Extension

If all else fails:
1. Remove the extension from `brave://extensions/`
2. Clear browser cache and storage
3. Reload the extension
4. Test with default snippets

## Getting Help

If the issue persists:

1. **Collect Information:**
   - Browser version (Brave/Chrome)
   - Extension version
   - Console error messages
   - Steps to reproduce

2. **Check Console Logs:**
   - Open Developer Tools
   - Check both popup and content script logs
   - Note any error messages

3. **Test Environment:**
   - Try on the test page first
   - Test on different websites
   - Check if it works on some pages but not others

## Common Error Messages

### "Extension context invalidated"
- **Cause:** Extension was reloaded while popup was open
- **Solution:** Close popup and try again

### "Cannot access contents of url"
- **Cause:** Page restrictions (some internal pages)
- **Solution:** Try on a regular website

### "Receiving end does not exist"
- **Cause:** Content script not loaded or page changed
- **Solution:** Refresh page and try again

## Debug Mode Features

When debug mode is enabled:
- Target elements are highlighted with blue borders
- Additional console logging is enabled
- Element detection is more verbose

To enable: `localStorage.setItem('snippetManagerDebug', 'true')`
To disable: `localStorage.removeItem('snippetManagerDebug')`
