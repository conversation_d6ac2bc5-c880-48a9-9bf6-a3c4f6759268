/**
 * Browser Polyfill for Cross-Browser Extension Compatibility
 * Provides a unified API that works with both Chrome and Firefox
 */

(function() {
    'use strict';

    // If browser API already exists (Firefox), use it directly
    if (typeof browser !== 'undefined' && browser.runtime) {
        // Firefox already has the browser API, no polyfill needed
        return;
    }

    // If chrome API exists but browser doesn't (Chrome), create browser polyfill
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        // Create browser namespace
        window.browser = {};

        // Helper function to promisify Chrome APIs
        function promisify(chromeApi, method) {
            return function(...args) {
                return new Promise((resolve, reject) => {
                    const callback = (result) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(result);
                        }
                    };
                    
                    // Call the Chrome API with callback
                    chromeApi[method](...args, callback);
                });
            };
        }

        // Helper function to wrap event listeners
        function wrapEventListener(chromeEvent) {
            return {
                addListener: chromeEvent.addListener.bind(chromeEvent),
                removeListener: chromeEvent.removeListener.bind(chromeEvent),
                hasListener: chromeEvent.hasListener.bind(chromeEvent)
            };
        }

        // Runtime API
        browser.runtime = {
            onInstalled: wrapEventListener(chrome.runtime.onInstalled),
            onStartup: wrapEventListener(chrome.runtime.onStartup),
            onMessage: wrapEventListener(chrome.runtime.onMessage),
            sendMessage: promisify(chrome.runtime, 'sendMessage'),
            getManifest: () => chrome.runtime.getManifest(),
            getURL: (path) => chrome.runtime.getURL(path),
            id: chrome.runtime.id,
            lastError: chrome.runtime.lastError
        };

        // Storage API
        browser.storage = {
            sync: {
                get: promisify(chrome.storage.sync, 'get'),
                set: promisify(chrome.storage.sync, 'set'),
                remove: promisify(chrome.storage.sync, 'remove'),
                clear: promisify(chrome.storage.sync, 'clear')
            },
            local: {
                get: promisify(chrome.storage.local, 'get'),
                set: promisify(chrome.storage.local, 'set'),
                remove: promisify(chrome.storage.local, 'remove'),
                clear: promisify(chrome.storage.local, 'clear')
            },
            onChanged: wrapEventListener(chrome.storage.onChanged)
        };

        // Tabs API
        browser.tabs = {
            query: promisify(chrome.tabs, 'query'),
            sendMessage: promisify(chrome.tabs, 'sendMessage'),
            onUpdated: wrapEventListener(chrome.tabs.onUpdated),
            get: promisify(chrome.tabs, 'get'),
            update: promisify(chrome.tabs, 'update'),
            create: promisify(chrome.tabs, 'create')
        };

        // Action API (for Manifest V3) or Browser Action API (for Manifest V2)
        const actionApi = chrome.action || chrome.browserAction;
        if (actionApi) {
            browser.action = browser.browserAction = {
                setBadgeText: promisify(actionApi, 'setBadgeText'),
                setBadgeBackgroundColor: promisify(actionApi, 'setBadgeBackgroundColor'),
                setTitle: promisify(actionApi, 'setTitle'),
                setIcon: promisify(actionApi, 'setIcon'),
                onClicked: wrapEventListener(actionApi.onClicked)
            };
        }

        // Notifications API
        if (chrome.notifications) {
            browser.notifications = {
                create: promisify(chrome.notifications, 'create'),
                clear: promisify(chrome.notifications, 'clear'),
                onClicked: wrapEventListener(chrome.notifications.onClicked),
                onClosed: wrapEventListener(chrome.notifications.onClosed)
            };
        }

        // Scripting API (Manifest V3)
        if (chrome.scripting) {
            browser.scripting = {
                executeScript: promisify(chrome.scripting, 'executeScript'),
                insertCSS: promisify(chrome.scripting, 'insertCSS'),
                removeCSS: promisify(chrome.scripting, 'removeCSS')
            };
        }

        console.log('Browser polyfill loaded for Chrome');
    } else {
        console.warn('Neither chrome nor browser API found');
    }
})();

// Also create a chrome polyfill for Firefox if needed
(function() {
    'use strict';
    
    // If chrome API doesn't exist but browser does (Firefox), create chrome polyfill
    if (typeof chrome === 'undefined' && typeof browser !== 'undefined' && browser.runtime) {
        window.chrome = browser;
        console.log('Chrome polyfill loaded for Firefox');
    }
})();
