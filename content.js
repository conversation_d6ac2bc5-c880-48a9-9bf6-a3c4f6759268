// Content script for Ticket Snippet Manager
// This script runs on all pages and helps with snippet insertion

class SnippetInserter {
    constructor() {
        this.init();
    }

    init() {
        // Listen for messages from the popup
        browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'insertSnippet') {
                try {
                    this.insertSnippet(request.text);
                    sendResponse({ success: true });
                } catch (error) {
                    console.error('Content script insertion error:', error);
                    sendResponse({ success: false, error: error.message });
                }
            } else if (request.action === 'checkTargets') {
                const targets = this.findTargetElements();
                sendResponse({
                    success: true,
                    targetCount: targets.length,
                    hasActiveElement: this.hasActiveEditableElement()
                });
            }
            return true; // Keep message channel open for async responses
        });

        // Add visual indicators for target elements (optional, for debugging)
        if (this.isDebugMode()) {
            this.highlightTargetElements();
        }
    }

    findTargetElements() {
        // Primary target: Froala editor elements
        const froalaElements = document.querySelectorAll('.fr-element.fr-view');
        
        // Secondary targets: other common editor elements
        const otherEditors = document.querySelectorAll([
            '[contenteditable="true"]',
            '.ql-editor', // Quill editor
            '.CodeMirror', // CodeMirror
            '.ace_editor', // Ace editor
            'textarea',
            'input[type="text"]'
        ].join(', '));

        // Combine and deduplicate
        const allTargets = [...froalaElements, ...otherEditors];
        return Array.from(new Set(allTargets));
    }

    hasActiveEditableElement() {
        const activeElement = document.activeElement;
        if (!activeElement) return false;

        return (
            activeElement.isContentEditable ||
            activeElement.tagName === 'TEXTAREA' ||
            (activeElement.tagName === 'INPUT' && activeElement.type === 'text') ||
            activeElement.classList.contains('fr-element') ||
            activeElement.classList.contains('ql-editor')
        );
    }

    insertSnippet(text) {
        console.log('Content script: Attempting to insert snippet:', text);

        try {
            // Find the best target element
            const targetElement = this.getBestTargetElement();

            if (!targetElement) {
                console.log('Content script: No target element found');
                throw new Error('No suitable target element found for snippet insertion');
            }

            console.log('Content script: Found target element:', targetElement.tagName, targetElement.className);

            // Insert the text based on element type
            if (this.isContentEditable(targetElement)) {
                console.log('Content script: Inserting into contenteditable element');
                this.insertIntoContentEditable(targetElement, text);
            } else if (this.isInputElement(targetElement)) {
                console.log('Content script: Inserting into input element');
                this.insertIntoInputElement(targetElement, text);
            } else {
                throw new Error('Target element type not supported');
            }

            // Trigger events to notify the application
            this.triggerChangeEvents(targetElement);

            // Show success feedback
            this.showInsertionFeedback(targetElement);

            console.log('Content script: Snippet insertion successful');

        } catch (error) {
            console.error('Snippet insertion failed:', error);
            this.showErrorFeedback(error.message);
            throw error; // Re-throw so the message handler can catch it
        }
    }

    getBestTargetElement() {
        // Priority 1: Currently focused element if it's editable
        const activeElement = document.activeElement;
        if (activeElement && this.isEditableElement(activeElement)) {
            return activeElement;
        }

        // Priority 2: Froala editor elements
        const froalaElements = document.querySelectorAll('.fr-element.fr-view');
        if (froalaElements.length > 0) {
            // Return the first visible Froala element
            for (const element of froalaElements) {
                if (this.isElementVisible(element)) {
                    return element;
                }
            }
        }

        // Priority 3: Other contenteditable elements
        const contentEditables = document.querySelectorAll('[contenteditable="true"]');
        for (const element of contentEditables) {
            if (this.isElementVisible(element)) {
                return element;
            }
        }

        // Priority 4: Text inputs and textareas
        const inputs = document.querySelectorAll('textarea, input[type="text"]');
        for (const element of inputs) {
            if (this.isElementVisible(element)) {
                return element;
            }
        }

        return null;
    }

    isEditableElement(element) {
        return (
            element.isContentEditable ||
            element.tagName === 'TEXTAREA' ||
            (element.tagName === 'INPUT' && element.type === 'text') ||
            element.classList.contains('fr-element')
        );
    }

    isContentEditable(element) {
        return element.isContentEditable || element.classList.contains('fr-element');
    }

    isInputElement(element) {
        return element.tagName === 'TEXTAREA' || 
               (element.tagName === 'INPUT' && element.type === 'text');
    }

    isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return (
            rect.width > 0 &&
            rect.height > 0 &&
            style.display !== 'none' &&
            style.visibility !== 'hidden' &&
            style.opacity !== '0'
        );
    }

    insertIntoContentEditable(element, text) {
        element.focus();

        // Try modern approach first
        if (document.execCommand && document.execCommand('insertText', false, text)) {
            return;
        }

        // Fallback for browsers that don't support execCommand
        const selection = window.getSelection();
        
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            range.deleteContents();
            
            // Create text node and insert
            const textNode = document.createTextNode(text);
            range.insertNode(textNode);
            
            // Move cursor to end of inserted text
            range.setStartAfter(textNode);
            range.setEndAfter(textNode);
            selection.removeAllRanges();
            selection.addRange(range);
        } else {
            // Last resort: append to element
            const textNode = document.createTextNode(text);
            element.appendChild(textNode);
        }
    }

    insertIntoInputElement(element, text) {
        element.focus();
        
        const start = element.selectionStart || 0;
        const end = element.selectionEnd || 0;
        const value = element.value || '';
        
        // Insert text at cursor position
        element.value = value.substring(0, start) + text + value.substring(end);
        
        // Move cursor to end of inserted text
        const newPosition = start + text.length;
        element.setSelectionRange(newPosition, newPosition);
    }

    triggerChangeEvents(element) {
        // Trigger various events that applications might listen for
        const events = ['input', 'change', 'keyup', 'blur'];
        
        events.forEach(eventType => {
            const event = new Event(eventType, { 
                bubbles: true, 
                cancelable: true 
            });
            element.dispatchEvent(event);
        });

        // Special handling for React and other frameworks
        if (element._valueTracker) {
            element._valueTracker.setValue('');
        }
    }

    showInsertionFeedback(element) {
        // Create a temporary visual indicator
        const indicator = document.createElement('div');
        indicator.textContent = '✓ Snippet inserted';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(indicator);

        // Remove after 2 seconds
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }, 2000);
    }

    showErrorFeedback(message) {
        const indicator = document.createElement('div');
        indicator.textContent = `✗ ${message}`;
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: opacity 0.3s ease;
        `;

        document.body.appendChild(indicator);

        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }, 3000);
    }

    highlightTargetElements() {
        const targets = this.findTargetElements();
        targets.forEach(element => {
            element.style.outline = '2px dashed #007bff';
            element.title = 'Snippet insertion target';
        });
    }

    isDebugMode() {
        return localStorage.getItem('snippetManagerDebug') === 'true';
    }
}

// Initialize the content script
let snippetInserter;
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        snippetInserter = new SnippetInserter();
        window.SnippetInserter = snippetInserter; // Expose for debugging
        console.log('Snippet Manager content script loaded');
    });
} else {
    snippetInserter = new SnippetInserter();
    window.SnippetInserter = snippetInserter; // Expose for debugging
    console.log('Snippet Manager content script loaded');
}
