// Debug script for Ticket Snippet Manager
// Run this in the browser console to test extension functionality

console.log('🔧 Ticket Snippet Manager Debug Script');
console.log('=====================================');

// Test 1: Check if extension APIs are available
function testExtensionAPI() {
    console.log('\n📋 Test 1: Extension API Availability');
    
    if (typeof chrome === 'undefined') {
        console.error('❌ Chrome API not available');
        return false;
    }
    
    console.log('✅ Chrome API available');
    
    const requiredAPIs = ['runtime', 'storage', 'tabs'];
    let allAvailable = true;
    
    requiredAPIs.forEach(api => {
        if (chrome[api]) {
            console.log(`✅ chrome.${api} available`);
        } else {
            console.error(`❌ chrome.${api} NOT available`);
            allAvailable = false;
        }
    });
    
    return allAvailable;
}

// Test 2: Check if content script is loaded
function testContentScript() {
    console.log('\n📋 Test 2: Content Script Status');
    
    if (window.SnippetInserter) {
        console.log('✅ Content script loaded and accessible');
        return true;
    } else {
        console.error('❌ Content script not loaded or not accessible');
        console.log('💡 Try refreshing the page and running this test again');
        return false;
    }
}

// Test 3: Check for target elements
function testTargetElements() {
    console.log('\n📋 Test 3: Target Element Detection');
    
    const froalaElements = document.querySelectorAll('.fr-element.fr-view');
    const contentEditables = document.querySelectorAll('[contenteditable="true"]');
    const textInputs = document.querySelectorAll('textarea, input[type="text"]');
    
    console.log(`📊 Found ${froalaElements.length} Froala elements`);
    console.log(`📊 Found ${contentEditables.length} contenteditable elements`);
    console.log(`📊 Found ${textInputs.length} text input elements`);
    
    const totalTargets = froalaElements.length + contentEditables.length + textInputs.length;
    
    if (totalTargets > 0) {
        console.log(`✅ Total target elements: ${totalTargets}`);
        
        // Show details about each target
        [...froalaElements, ...contentEditables, ...textInputs].forEach((el, index) => {
            console.log(`   ${index + 1}. ${el.tagName} ${el.className || '(no class)'}`);
        });
        
        return true;
    } else {
        console.error('❌ No target elements found on this page');
        console.log('💡 Try navigating to a page with text inputs or contenteditable elements');
        return false;
    }
}

// Test 4: Test content script message handling
async function testContentScriptMessage() {
    console.log('\n📋 Test 4: Content Script Message Handling');
    
    if (!window.SnippetInserter) {
        console.error('❌ Content script not available for testing');
        return false;
    }
    
    try {
        // Test the insertSnippet method directly
        console.log('🧪 Testing direct snippet insertion...');
        window.SnippetInserter.insertSnippet('TEST SNIPPET - Direct Call');
        console.log('✅ Direct insertion test completed (check for visual feedback)');
        return true;
    } catch (error) {
        console.error('❌ Direct insertion test failed:', error);
        return false;
    }
}

// Test 5: Test popup-to-content communication
async function testPopupCommunication() {
    console.log('\n📋 Test 5: Popup-to-Content Communication');
    
    if (!chrome.tabs) {
        console.error('❌ chrome.tabs API not available');
        return false;
    }
    
    try {
        // Get current tab
        const tabs = await new Promise((resolve) => {
            chrome.tabs.query({active: true, currentWindow: true}, resolve);
        });
        
        if (tabs.length === 0) {
            console.error('❌ No active tab found');
            return false;
        }
        
        const tab = tabs[0];
        console.log(`📋 Active tab: ${tab.id} - ${tab.url}`);
        
        // Send test message
        console.log('🧪 Sending test message to content script...');
        const response = await new Promise((resolve) => {
            chrome.tabs.sendMessage(tab.id, {
                action: 'insertSnippet',
                text: 'TEST SNIPPET - Message Passing'
            }, resolve);
        });
        
        if (chrome.runtime.lastError) {
            console.error('❌ Message sending failed:', chrome.runtime.lastError.message);
            return false;
        }
        
        if (response && response.success) {
            console.log('✅ Message communication successful');
            return true;
        } else {
            console.error('❌ Message communication failed:', response);
            return false;
        }
        
    } catch (error) {
        console.error('❌ Communication test error:', error);
        return false;
    }
}

// Test 6: Test storage functionality
async function testStorage() {
    console.log('\n📋 Test 6: Storage Functionality');
    
    try {
        // Test storage write
        const testData = { test: 'snippet', timestamp: Date.now() };
        await new Promise((resolve) => {
            chrome.storage.sync.set({ debugTest: testData }, resolve);
        });
        
        // Test storage read
        const result = await new Promise((resolve) => {
            chrome.storage.sync.get(['debugTest'], resolve);
        });
        
        if (result.debugTest && result.debugTest.test === 'snippet') {
            console.log('✅ Storage read/write successful');
            
            // Clean up
            chrome.storage.sync.remove(['debugTest']);
            return true;
        } else {
            console.error('❌ Storage test failed - data mismatch');
            return false;
        }
        
    } catch (error) {
        console.error('❌ Storage test error:', error);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Running all extension tests...\n');
    
    const results = {
        extensionAPI: testExtensionAPI(),
        contentScript: testContentScript(),
        targetElements: testTargetElements(),
        contentScriptMessage: await testContentScriptMessage(),
        popupCommunication: await testPopupCommunication(),
        storage: await testStorage()
    };
    
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    let passedTests = 0;
    const totalTests = Object.keys(results).length;
    
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${test}`);
        if (passed) passedTests++;
    });
    
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! The extension should be working correctly.');
    } else {
        console.log('⚠️  Some tests failed. Check the individual test results above.');
        console.log('💡 Common solutions:');
        console.log('   - Refresh the page and try again');
        console.log('   - Reload the extension in chrome://extensions/');
        console.log('   - Check the browser console for error messages');
        console.log('   - Make sure you\'re on a page with editable elements');
    }
    
    return results;
}

// Quick fix function
function quickFix() {
    console.log('🔧 Running quick fixes...');
    
    // Enable debug mode
    localStorage.setItem('snippetManagerDebug', 'true');
    console.log('✅ Debug mode enabled');
    
    // Focus first available target element
    const target = document.querySelector('.fr-element.fr-view, [contenteditable="true"], textarea, input[type="text"]');
    if (target) {
        target.focus();
        console.log('✅ Focused first available target element');
    }
    
    console.log('💡 Refresh the page to see debug highlights');
}

// Export functions for manual use
window.extensionDebug = {
    runAllTests,
    testExtensionAPI,
    testContentScript,
    testTargetElements,
    testContentScriptMessage,
    testPopupCommunication,
    testStorage,
    quickFix
};

console.log('\n🎮 Available commands:');
console.log('extensionDebug.runAllTests() - Run all tests');
console.log('extensionDebug.quickFix() - Apply quick fixes');
console.log('extensionDebug.testContentScript() - Test content script only');

// Auto-run tests if this script is executed
if (typeof window !== 'undefined') {
    setTimeout(() => {
        runAllTests();
    }, 1000);
}
