<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firefox Extension Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        textarea, input[type="text"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .contenteditable {
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            min-height: 100px;
            background: #f9f9f9;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>Firefox Extension Test Page</h1>
    
    <div class="instructions">
        <h3>Testing Instructions:</h3>
        <ol>
            <li>Load the extension in Firefox using about:debugging</li>
            <li>Click on the extension icon in the toolbar</li>
            <li>Add a test snippet in the popup</li>
            <li>Click on different input fields below and try inserting the snippet</li>
            <li>Verify that the snippet is inserted correctly in each field type</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>Test 1: Regular Text Input</h3>
        <input type="text" placeholder="Click here and try inserting a snippet..." id="textInput">
    </div>

    <div class="test-section">
        <h3>Test 2: Textarea</h3>
        <textarea placeholder="Click here and try inserting a snippet..." id="textarea"></textarea>
    </div>

    <div class="test-section">
        <h3>Test 3: Contenteditable Div</h3>
        <div class="contenteditable" contenteditable="true" id="contenteditable">
            Click here and try inserting a snippet...
        </div>
    </div>

    <div class="test-section">
        <h3>Test 4: Simulated Rich Text Editor</h3>
        <div class="contenteditable fr-element fr-view" contenteditable="true" id="richEditor">
            This simulates a Froala editor. Click here and try inserting a snippet...
        </div>
    </div>

    <div id="testResults" class="test-section">
        <h3>Test Results:</h3>
        <div id="results"></div>
    </div>

    <script>
        // Add some basic testing functionality
        let testCount = 0;
        let passedTests = 0;

        function addResult(test, status, message) {
            testCount++;
            if (status === 'pass') passedTests++;
            
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${status === 'pass' ? 'success' : 'error'}`;
            div.innerHTML = `<strong>Test ${testCount}: ${test}</strong> - ${message}`;
            results.appendChild(div);
        }

        // Test if extension is loaded
        function checkExtensionLoaded() {
            // This is a basic check - in a real scenario, you'd need to communicate with the extension
            setTimeout(() => {
                addResult('Extension Detection', 'info', 'Please manually verify the extension icon appears in the toolbar');
            }, 1000);
        }

        // Add event listeners to track interactions
        document.querySelectorAll('input, textarea, [contenteditable]').forEach(element => {
            element.addEventListener('focus', () => {
                console.log('Focused element:', element.tagName, element.className);
            });
            
            element.addEventListener('input', () => {
                console.log('Input detected in:', element.tagName, element.className);
            });
        });

        // Run basic checks when page loads
        document.addEventListener('DOMContentLoaded', () => {
            checkExtensionLoaded();
            
            // Add a summary after a delay
            setTimeout(() => {
                const summary = document.createElement('div');
                summary.className = 'status info';
                summary.innerHTML = `<strong>Manual Testing Required:</strong> Please test snippet insertion in each field above using the extension popup.`;
                document.getElementById('results').appendChild(summary);
            }, 2000);
        });
    </script>
</body>
</html>
