/**
 * Firefox Extension Validation Script
 * Run this in the browser console to validate Firefox compatibility
 */

function validateFirefoxCompatibility() {
    const results = [];
    
    // Check if browser API is available
    if (typeof browser !== 'undefined' && browser.runtime) {
        results.push({ test: 'Browser API', status: 'PASS', message: 'browser.* API is available' });
    } else if (typeof chrome !== 'undefined' && chrome.runtime) {
        results.push({ test: 'Browser API', status: 'WARN', message: 'Using chrome.* API (polyfill should handle this)' });
    } else {
        results.push({ test: 'Browser API', status: 'FAIL', message: 'No extension API available' });
    }
    
    // Check manifest version
    try {
        const manifest = (browser || chrome).runtime.getManifest();
        if (manifest.manifest_version === 2) {
            results.push({ test: 'Manifest Version', status: 'PASS', message: 'Using Manifest V2 (Firefox compatible)' });
        } else {
            results.push({ test: 'Manifest Version', status: 'WARN', message: `Using Manifest V${manifest.manifest_version} (may have limited Firefox support)` });
        }
    } catch (error) {
        results.push({ test: 'Manifest Version', status: 'FAIL', message: 'Cannot read manifest: ' + error.message });
    }
    
    // Check storage API
    try {
        const api = browser || chrome;
        if (api.storage && api.storage.sync) {
            results.push({ test: 'Storage API', status: 'PASS', message: 'Storage API available' });
        } else {
            results.push({ test: 'Storage API', status: 'FAIL', message: 'Storage API not available' });
        }
    } catch (error) {
        results.push({ test: 'Storage API', status: 'FAIL', message: 'Storage API error: ' + error.message });
    }
    
    // Check tabs API
    try {
        const api = browser || chrome;
        if (api.tabs && api.tabs.query) {
            results.push({ test: 'Tabs API', status: 'PASS', message: 'Tabs API available' });
        } else {
            results.push({ test: 'Tabs API', status: 'FAIL', message: 'Tabs API not available' });
        }
    } catch (error) {
        results.push({ test: 'Tabs API', status: 'FAIL', message: 'Tabs API error: ' + error.message });
    }
    
    // Display results
    console.group('Firefox Compatibility Validation Results');
    results.forEach(result => {
        const style = result.status === 'PASS' ? 'color: green' : 
                     result.status === 'WARN' ? 'color: orange' : 'color: red';
        console.log(`%c${result.status}: ${result.test} - ${result.message}`, style);
    });
    console.groupEnd();
    
    return results;
}

// Auto-run if in extension context
if (typeof browser !== 'undefined' || typeof chrome !== 'undefined') {
    validateFirefoxCompatibility();
}
