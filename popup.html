<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Snippet Manager</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Snippet Manager</h1>
            <button id="addSnippetBtn" class="btn btn-primary">+ Add Snippet</button>
        </header>

        <!-- Add/Edit Snippet Form -->
        <div id="snippetForm" class="form-container hidden">
            <h3 id="formTitle">Add New Snippet</h3>
            <form id="snippetFormElement">
                <div class="form-group">
                    <label for="snippetName">Name:</label>
                    <input type="text" id="snippetName" placeholder="Enter snippet name" required>
                </div>
                <div class="form-group">
                    <label for="snippetText">Text:</label>
                    <textarea id="snippetText" placeholder="Enter snippet text" required></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="saveBtn">Save</button>
                    <button type="button" class="btn btn-secondary" id="cancelBtn">Cancel</button>
                </div>
            </form>
        </div>

        <!-- Snippets List -->
        <div id="snippetsList" class="snippets-list">
            <div id="emptyState" class="empty-state">
                <p>No snippets saved yet.</p>
                <p>Click "Add Snippet" to get started!</p>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div id="message" class="message hidden"></div>
    </div>

    <script src="browser-polyfill.js"></script>
    <script src="popup.js"></script>
</body>
</html>
