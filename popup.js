class SnippetManager {
    constructor() {
        this.snippets = [];
        this.editingId = null;
        this.init();
    }

    async init() {
        await this.loadSnippets();
        this.bindEvents();
        this.renderSnippets();
    }

    async loadSnippets() {
        try {
            const result = await chrome.storage.sync.get(['snippets']);
            this.snippets = result.snippets || [];
        } catch (error) {
            console.error('Error loading snippets:', error);
            this.showMessage('Error loading snippets', 'error');
        }
    }

    async saveSnippets() {
        try {
            await chrome.storage.sync.set({ snippets: this.snippets });
        } catch (error) {
            console.error('Error saving snippets:', error);
            this.showMessage('Error saving snippets', 'error');
        }
    }

    bindEvents() {
        // Add snippet button
        document.getElementById('addSnippetBtn').addEventListener('click', () => {
            this.showForm();
        });

        // Form submission
        document.getElementById('snippetFormElement').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSnippet();
        });

        // Cancel button
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.hideForm();
        });
    }

    showForm(snippet = null) {
        const form = document.getElementById('snippetForm');
        const title = document.getElementById('formTitle');
        const nameInput = document.getElementById('snippetName');
        const textInput = document.getElementById('snippetText');

        if (snippet) {
            title.textContent = 'Edit Snippet';
            nameInput.value = snippet.name;
            textInput.value = snippet.text;
            this.editingId = snippet.id;
        } else {
            title.textContent = 'Add New Snippet';
            nameInput.value = '';
            textInput.value = '';
            this.editingId = null;
        }

        form.classList.remove('hidden');
        nameInput.focus();
    }

    hideForm() {
        document.getElementById('snippetForm').classList.add('hidden');
        this.editingId = null;
    }

    async saveSnippet() {
        const name = document.getElementById('snippetName').value.trim();
        const text = document.getElementById('snippetText').value.trim();

        if (!name || !text) {
            this.showMessage('Please fill in all fields', 'error');
            return;
        }

        if (this.editingId) {
            // Edit existing snippet
            const index = this.snippets.findIndex(s => s.id === this.editingId);
            if (index !== -1) {
                this.snippets[index] = { ...this.snippets[index], name, text };
                this.showMessage('Snippet updated successfully', 'success');
            }
        } else {
            // Add new snippet
            const snippet = {
                id: Date.now().toString(),
                name,
                text,
                createdAt: new Date().toISOString()
            };
            this.snippets.push(snippet);
            this.showMessage('Snippet saved successfully', 'success');
        }

        await this.saveSnippets();
        this.hideForm();
        this.renderSnippets();
    }

    async deleteSnippet(id) {
        if (confirm('Are you sure you want to delete this snippet?')) {
            this.snippets = this.snippets.filter(s => s.id !== id);
            await this.saveSnippets();
            this.renderSnippets();
            this.showMessage('Snippet deleted successfully', 'success');
        }
    }

    async insertSnippet(snippet) {
        console.log('Popup: Attempting to insert snippet:', snippet);

        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            console.log('Popup: Active tab:', tab.id, tab.url);

            // Show visual feedback
            const snippetElement = document.querySelector(`[data-snippet-id="${snippet.id}"]`);
            if (snippetElement) {
                snippetElement.classList.add('inserting');
                setTimeout(() => {
                    snippetElement.classList.remove('inserting');
                }, 1000);
            }

            // Send message to content script to insert the snippet
            console.log('Popup: Sending message to content script');
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'insertSnippet',
                text: snippet.text
            });

            console.log('Popup: Received response from content script:', response);

            if (response && response.success) {
                this.showMessage('Snippet inserted successfully', 'success');
            } else {
                const errorMsg = response?.error || 'Content script did not respond or failed';
                throw new Error(errorMsg);
            }
        } catch (error) {
            console.error('Error inserting snippet:', error);

            // More specific error messages
            if (error.message.includes('Could not establish connection')) {
                this.showMessage('Error: Content script not loaded. Please refresh the page and try again.', 'error');
            } else if (error.message.includes('No suitable target element')) {
                this.showMessage('Error: No editable element found on this page.', 'error');
            } else {
                this.showMessage(`Error inserting snippet: ${error.message}`, 'error');
            }
        }
    }



    renderSnippets() {
        const container = document.getElementById('snippetsList');
        const emptyState = document.getElementById('emptyState');

        if (this.snippets.length === 0) {
            emptyState.classList.remove('hidden');
            return;
        }

        emptyState.classList.add('hidden');

        const snippetsHtml = this.snippets.map(snippet => `
            <div class="snippet-item" data-snippet-id="${snippet.id}">
                <div class="snippet-header">
                    <div class="snippet-name">${this.escapeHtml(snippet.name)}</div>
                    <div class="snippet-actions">
                        <button class="btn btn-edit" data-action="edit" data-id="${snippet.id}">Edit</button>
                        <button class="btn btn-danger" data-action="delete" data-id="${snippet.id}">Delete</button>
                    </div>
                </div>
                <div class="snippet-text">${this.escapeHtml(snippet.text)}</div>
            </div>
        `).join('');

        container.innerHTML = snippetsHtml + emptyState.outerHTML;

        // Bind click events
        container.addEventListener('click', (e) => {
            const snippetItem = e.target.closest('.snippet-item');
            const action = e.target.dataset.action;
            const id = e.target.dataset.id;

            if (action === 'edit') {
                const snippet = this.snippets.find(s => s.id === id);
                if (snippet) this.showForm(snippet);
            } else if (action === 'delete') {
                this.deleteSnippet(id);
            } else if (snippetItem && !action) {
                // Click on snippet item to insert
                const snippet = this.snippets.find(s => s.id === snippetItem.dataset.snippetId);
                if (snippet) this.insertSnippet(snippet);
            }
        });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showMessage(text, type) {
        const messageEl = document.getElementById('message');
        messageEl.textContent = text;
        messageEl.className = `message ${type}`;
        messageEl.classList.remove('hidden');

        setTimeout(() => {
            messageEl.classList.add('hidden');
        }, 3000);
    }
}

// Initialize the snippet manager when the popup loads
document.addEventListener('DOMContentLoaded', () => {
    new SnippetManager();
});
