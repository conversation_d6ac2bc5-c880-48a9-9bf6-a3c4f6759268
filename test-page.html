<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snippet Manager Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .fr-element.fr-view {
            border: 2px solid #007bff;
            padding: 15px;
            min-height: 100px;
            background: white;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .fr-element.fr-view:focus {
            outline: none;
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
        }
        
        textarea, input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 10px 0;
            font-family: inherit;
        }
        
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin-bottom: 30px;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Ticket Snippet Manager - Test Page</h1>
    
    <div class="instructions">
        <h3>Testing Instructions</h3>
        <ol>
            <li>Make sure the Ticket Snippet Manager extension is installed and enabled</li>
            <li>Click the extension icon in your browser toolbar to open the popup</li>
            <li>Add some test snippets if you haven't already</li>
            <li>Click in any of the editable areas below</li>
            <li>Click the extension icon again and select a snippet to insert</li>
            <li>Verify that the snippet text appears in the focused element</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h3>Primary Target: Froala Editor Simulation</h3>
        <p>This element has the <code>fr-element fr-view</code> classes that the extension primarily targets:</p>
        <div class="fr-element fr-view" contenteditable="true" placeholder="Click here and then use the extension to insert a snippet...">
            Click here and then use the extension to insert a snippet...
        </div>
        <div class="status info">
            Status: This is the primary target for snippet insertion
        </div>
    </div>
    
    <div class="test-section">
        <h3>Secondary Target: Generic Contenteditable</h3>
        <p>This is a generic contenteditable element:</p>
        <div contenteditable="true" style="border: 1px solid #ccc; padding: 10px; min-height: 80px; background: white;">
            Generic contenteditable area...
        </div>
        <div class="status info">
            Status: Secondary target for snippet insertion
        </div>
    </div>
    
    <div class="test-section">
        <h3>Fallback Targets: Standard Form Elements</h3>
        <p>These are standard form elements that should work as fallbacks:</p>
        
        <label for="textInput">Text Input:</label>
        <input type="text" id="textInput" placeholder="Text input field...">
        
        <label for="textArea">Textarea:</label>
        <textarea id="textArea" placeholder="Textarea field..."></textarea>
        
        <div class="status info">
            Status: Fallback targets for snippet insertion
        </div>
    </div>
    
    <div class="test-section">
        <h3>Multiple Froala Elements</h3>
        <p>Test with multiple target elements to ensure the extension selects the focused one:</p>
        
        <div class="fr-element fr-view" contenteditable="true">
            First Froala element...
        </div>
        
        <div class="fr-element fr-view" contenteditable="true">
            Second Froala element...
        </div>
        
        <div class="status info">
            Status: Test focused element selection
        </div>
    </div>
    
    <div class="test-section">
        <h3>Debug Information</h3>
        <button onclick="runDebugTests()">Run Debug Tests</button>
        <button onclick="enableDebugMode()">Enable Debug Mode</button>
        <button onclick="disableDebugMode()">Disable Debug Mode</button>
        <button onclick="testContentScript()">Test Content Script</button>
        
        <div id="debugOutput" class="debug-info">
            Click "Run Debug Tests" to see debug information...
        </div>
    </div>
    
    <script>
        function runDebugTests() {
            const output = document.getElementById('debugOutput');
            let info = [];

            // Check for target elements
            const froalaElements = document.querySelectorAll('.fr-element.fr-view');
            const contentEditables = document.querySelectorAll('[contenteditable="true"]');
            const textInputs = document.querySelectorAll('textarea, input[type="text"]');

            info.push(`Found ${froalaElements.length} Froala elements (.fr-element.fr-view)`);
            info.push(`Found ${contentEditables.length} contenteditable elements`);
            info.push(`Found ${textInputs.length} text input elements`);

            // Check active element
            const activeElement = document.activeElement;
            info.push(`Active element: ${activeElement.tagName} ${activeElement.className || '(no class)'}`);

            // Check if extension is loaded
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                info.push('Chrome extension API available');

                // Test content script communication
                chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                    if (chrome.runtime.lastError) {
                        info.push(`Extension communication error: ${chrome.runtime.lastError.message}`);
                    } else {
                        info.push('Extension communication: OK');
                    }
                    output.innerHTML = info.join('\n');
                });
            } else {
                info.push('Chrome extension API NOT available');
            }

            // Check debug mode
            const debugMode = localStorage.getItem('snippetManagerDebug');
            info.push(`Debug mode: ${debugMode || 'disabled'}`);

            // Check if content script is loaded
            if (window.SnippetInserter) {
                info.push('Content script: LOADED');
            } else {
                info.push('Content script: NOT LOADED');
            }

            output.innerHTML = info.join('\n');
        }
        
        function enableDebugMode() {
            localStorage.setItem('snippetManagerDebug', 'true');
            alert('Debug mode enabled. Reload the page to see target element highlights.');
        }
        
        function disableDebugMode() {
            localStorage.removeItem('snippetManagerDebug');
            alert('Debug mode disabled. Reload the page to remove highlights.');
        }

        function testContentScript() {
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                // Test sending a message to content script
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    chrome.tabs.sendMessage(tabs[0].id, {
                        action: 'insertSnippet',
                        text: 'TEST SNIPPET FROM DEBUG'
                    }, function(response) {
                        if (chrome.runtime.lastError) {
                            alert('Content script error: ' + chrome.runtime.lastError.message);
                        } else if (response && response.success) {
                            alert('Content script test successful!');
                        } else {
                            alert('Content script test failed: ' + (response?.error || 'Unknown error'));
                        }
                    });
                });
            } else {
                alert('Chrome extension API not available');
            }
        }
        
        // Add focus event listeners to show which element is active
        document.addEventListener('focusin', function(e) {
            // Remove previous highlights
            document.querySelectorAll('.focused').forEach(el => {
                el.classList.remove('focused');
            });
            
            // Highlight focused element
            if (e.target.matches('.fr-element, [contenteditable], textarea, input[type="text"]')) {
                e.target.classList.add('focused');
                e.target.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.25)';
            }
        });
        
        document.addEventListener('focusout', function(e) {
            if (e.target.matches('.fr-element, [contenteditable], textarea, input[type="text"]')) {
                e.target.style.boxShadow = '';
            }
        });
        
        // Run initial debug test
        setTimeout(runDebugTests, 1000);
    </script>
</body>
</html>
